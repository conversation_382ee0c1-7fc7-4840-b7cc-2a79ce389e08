#!/usr/bin/env python3
import base64
import zlib

# The obfuscated payload from the decompiled code
payload = b'=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'

# Decode the lambda function logic: reverse the string, base64 decode, then zlib decompress
def decode_payload(data):
    # Reverse the string ([::-1])
    reversed_data = data[::-1]
    # Base64 decode
    decoded = base64.b64decode(reversed_data)
    # Zlib decompress
    decompressed = zlib.decompress(decoded)
    return decompressed

# Decode the payload
try:
    result = decode_payload(payload)
    print("First layer decoded payload:")
    print(result.decode('utf-8'))
    print("\n" + "="*50 + "\n")

    # Extract the second layer payload
    second_payload = b'=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'

    print("Second layer decoded payload:")
    second_result = decode_payload(second_payload)
    print(second_result.decode('utf-8'))
    print("\n" + "="*50 + "\n")

    # Extract the third layer payload
    third_payload = b'=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'

    print("Third layer decoded payload:")
    third_result = decode_payload(third_payload)
    print(third_result.decode('utf-8'))

except Exception as e:
    print(f"Error decoding: {e}")
