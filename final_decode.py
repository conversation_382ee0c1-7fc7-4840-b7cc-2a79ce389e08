#!/usr/bin/env python3
import base64
import zlib

# The lambda function from the decompiled code
_ = lambda __: __import__('zlib').decompress(__import__('base64').b64decode(__[::-1]))

# Seventh layer
seventh_payload = b'==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'

print("Decoding seventh layer...")
try:
    seventh_result = _(seventh_payload)
    print("Seventh layer decoded successfully!")
    result_str = seventh_result.decode('utf-8')
    print(result_str)
    
    # Check if there's another layer
    if result_str.startswith('exec((_)(b\''):
        print("\nFound eighth layer!")
        # Extract the payload
        start = result_str.find("b'") + 2
        end = result_str.rfind("'))")
        eighth_payload = result_str[start:end].encode('utf-8')

        print("Decoding eighth layer...")
        try:
            eighth_result = _(eighth_payload)
            print("Eighth layer decoded successfully!")
            final_result = eighth_result.decode('utf-8')
            print(final_result)

            # Check if there's another layer
            if final_result.startswith('exec((_)(b\''):
                print("\nFound ninth layer!")
                # Extract the payload
                start = final_result.find("b'") + 2
                end = final_result.rfind("'))")
                ninth_payload = final_result[start:end].encode('utf-8')

                print("Decoding ninth layer...")
                try:
                    ninth_result = _(ninth_payload)
                    print("Ninth layer decoded successfully!")
                    actual_final_result = ninth_result.decode('utf-8')
                    print(actual_final_result)

                    # Check if there's another layer
                    if actual_final_result.startswith('exec((_)(b\''):
                        print("\nFound tenth layer!")
                        # Extract the payload
                        start = actual_final_result.find("b'") + 2
                        end = actual_final_result.rfind("'))")
                        tenth_payload = actual_final_result[start:end].encode('utf-8')

                        print("Decoding tenth layer...")
                        try:
                            tenth_result = _(tenth_payload)
                            print("Tenth layer decoded successfully!")
                            truly_final_result = tenth_result.decode('utf-8')
                            print(truly_final_result)

                            # Check if there's another layer
                            if truly_final_result.startswith('exec((_)(b\''):
                                print("\nFound eleventh layer!")
                                # Extract the payload
                                start = truly_final_result.find("b'") + 2
                                end = truly_final_result.rfind("'))")
                                eleventh_payload = truly_final_result[start:end].encode('utf-8')

                                print("Decoding eleventh layer...")
                                try:
                                    eleventh_result = _(eleventh_payload)
                                    print("Eleventh layer decoded successfully!")
                                    absolutely_final_result = eleventh_result.decode('utf-8')
                                    print(absolutely_final_result)

                                    # Check if there's another layer
                                    if absolutely_final_result.startswith('exec((_)(b\''):
                                        print("\nFound twelfth layer!")
                                        # Extract the payload
                                        start = absolutely_final_result.find("b'") + 2
                                        end = absolutely_final_result.rfind("'))")
                                        twelfth_payload = absolutely_final_result[start:end].encode('utf-8')

                                        print("Decoding twelfth layer...")
                                        try:
                                            twelfth_result = _(twelfth_payload)
                                            print("Twelfth layer decoded successfully!")
                                            ultimate_final_result = twelfth_result.decode('utf-8')
                                            print(ultimate_final_result)

                                            # Save the final decoded result to a file for analysis
                                            with open('final_encryption_code.py', 'w') as f:
                                                f.write(ultimate_final_result)
                                            print("\nSaved final code to final_encryption_code.py")

                                        except Exception as e:
                                            print(f"Error decoding twelfth layer: {e}")
                                    else:
                                        # Save the final decoded result to a file for analysis
                                        with open('final_encryption_code.py', 'w') as f:
                                            f.write(absolutely_final_result)
                                        print("\nSaved final code to final_encryption_code.py")

                                except Exception as e:
                                    print(f"Error decoding eleventh layer: {e}")
                            else:
                                # Save the final decoded result to a file for analysis
                                with open('final_encryption_code.py', 'w') as f:
                                    f.write(truly_final_result)
                                print("\nSaved final code to final_encryption_code.py")

                        except Exception as e:
                            print(f"Error decoding tenth layer: {e}")
                    else:
                        # Save the final decoded result to a file for analysis
                        with open('final_encryption_code.py', 'w') as f:
                            f.write(actual_final_result)
                        print("\nSaved final code to final_encryption_code.py")

                except Exception as e:
                    print(f"Error decoding ninth layer: {e}")
            else:
                # Save the final decoded result to a file for analysis
                with open('final_encryption_code.py', 'w') as f:
                    f.write(final_result)
                print("\nSaved final code to final_encryption_code.py")

        except Exception as e:
            print(f"Error decoding eighth layer: {e}")
    else:
        # Save the final decoded result to a file for analysis
        with open('final_encryption_code.py', 'w') as f:
            f.write(result_str)
        print("\nSaved final code to final_encryption_code.py")

except Exception as e:
    print(f"Error decoding seventh layer: {e}")
