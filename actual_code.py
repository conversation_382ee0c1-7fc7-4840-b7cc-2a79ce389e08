#!/usr/bin/env python3
import base64
import zlib

# The lambda function from the decompiled code
_ = lambda __: __import__('zlib').decompress(__import__('base64').b64decode(__[::-1]))

# The third layer payload that failed to decode - this might be the actual code
third_payload = b'=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'

# Let's try to decode this manually step by step
print("Trying to decode the third layer manually...")

try:
    # Step 1: Reverse the string
    reversed_data = third_payload[::-1]
    print(f"Reversed data length: {len(reversed_data)}")
    
    # Step 2: Base64 decode
    decoded = base64.b64decode(reversed_data)
    print(f"Base64 decoded length: {len(decoded)}")
    
    # Step 3: Try to decompress
    try:
        decompressed = zlib.decompress(decoded)
        print("Successfully decompressed!")
        print(decompressed.decode('utf-8'))
    except Exception as e:
        print(f"Zlib decompression failed: {e}")
        # Maybe it's not compressed, let's try to decode as text
        try:
            print("Trying to decode as raw text:")
            print(decoded.decode('utf-8'))
        except:
            print("Raw decode failed too. Let's examine the hex:")
            print(decoded[:100].hex())
            
except Exception as e:
    print(f"Error in manual decode: {e}")

print("\n" + "="*50 + "\n")

# Now let's decode the fourth layer
fourth_payload = b'==g4nAI+/sv///p6VzLMoqg0EgG0U53n/gxe2nx7N+XDUeF1vCSm9PILWzLur2w8y/PUMYguBDwFqfyEqdLX/y76dix0t3d2gwrhWzhIe5NTh0p45PamZ7WuvsudqLE2gqFmPnZVlWXtstL+Rjj5kzUQhAnxzjAKML51zXjq1H13l21O/Rw0whhLRujKm7pN243eV75iiCfoAOnkckxzXcPeO4imHYSzVqobhm3OyrHLjBXqwHxuBr/tJOtfctpzvE4eXflL0VZLQx/ownrhlOJGIiRULcM0wrO5Y0DuRbueBf3o6MH8atvafMckihk1WDSO6ZSI1NciE/r5W8Rf67oWDreCvBsMCTmh9pmij+8cJmIGVaRJU7gUrykrMURgtr5zWFOM56qUv+c71XdeyfQTuElQ5s/fARPCIa9rBuwSn+I+VgE5XXInjXrt6j1rd9HA9hg0HN3o/WWx1QBHZPCiEsZIaYgcx60jsUmLZNwm+ORma6bnNKkR87jpwZnlHcaBPpRNr3qknrqdrbkFaD1Sw7+Si0sZff6UzTYTYvWQXJnZANOn4nhLSk3D86DQQD9mILV0ymgNrw77+WeIi9gESo85DvRU7sBlFzEhwKdQ8j/UH7+VhZrbGznkA9cqERdXxRWeYQasPNxYFcbbWuS9mMNZpZtRU82KRUG8QdHSpmil3i4nEFWHR1fjIlP85jTL3EQdXi63fT51s4bk1kKc+k+iHHR4UBq19IqhS8vb5FJt4lEE5JYvKXt9at1bQ6x88/oGR4DAc5uR0MUl4eHmFga+fDpLfpWq51ZlyDbcqhwIWEuneaMyhlsagWwSt1xF+A0MhOdxSImoyzMV9ourK/fNleIdfpvba+8FHr/DnKHAFKoponqUPu7J8KPC6WCg9ovbOypGFPh3+JpFQ211LwnjTkBKpVsb7C9zeEmPdVtntAIRzDyK7GxSXwSf/6ad2w1HZzlJX7ndFccdUWUj9qneGDUXIWlFPtCYG3Jcd8CRHIj8BcPawSjLdtJ/x2eVXYxhwlHZktag9hLPVm4eJMAJqeu5EYKuRokwsG7H4E9gdj+rsLWh3ghMDrNDqaQz3+QN8qgKbl2r1XrRi/LxIgdLF4zJ6vprjiqyE2/dZ9U1vzxd8qRinnvcW3wVnHRDZLd7DWcfQENQH27VM3yRbic9JPVkABGMmRyGGdXHd65sfvtu65uLwNCd3vE4zZUy+iXE+6Kx8H9P2MkzA7TmZj/QqVTpFBxdz1Z2KJQB9gxmvHZPWaZ0f67D7vS1RJDmT3dUfaKNh/bUqB29BstGBjCWdoHtEtLfY8cWpPHrXcb0vJY8+APiiAIgNzwfccmsVy91a7b4Xc4/hNY4oYkoQeLprv6W7fi0MILChNqmdlWz7E6pwRT7nYdGzAEE+4twaM+ZX/KpEOCkNs/P2CTm+MFoewcFZYvHfbuMWJ8EdU1JiSrPZxGE/On1Bq0cl442VViKHBfp7xdjtbT/p9rEAFUF+tZeU91Uq5p1JXF2eXFlUqW7FDiZ/5o3vwwH3HrAuiDjHXDklfu/zpWANjPL5BTXSkA/jkOR5kpUT2BC1TyJwvK2r8FCNsJ3HDHXUMLFSzFuZf84yGn6qKB3dcDdN2Z0/3sSN1SG0/YIg1MIsnsygW6DYeXTjBVaX48etPUtLzujbycEWBRUJoltsnyW992HG8HtVtMDuD9mnNKSK7jFQxSCYt/eU1v4d5Y+qXdK2ew/nsyg4qL8qI99W7NOnZumXPU6yTzL6K4hR6KsjWctt2km6c/4JD5F78e7e+3SfBHSccRaq89zD3yodI+u/1SllRMb6BCq94Ql870tyNO7qb6BtJ4sjo1G3LxdxbYoKEn74PpUNKGLwP3dvFtvPnSoTEC24Ul2I6hGe/yJbNCIq8sCdPfl/Lqg+Dp42NCA4En2ze77j9GHZXC4TFr/exn9x5SlQQw8YdD7wQJzXKyY/45QNbLjdKEsX6G1nsV83/mE46V6bSCS2DprPzayKo0leEisGDjcogtZWNLYX1hhGWGHj6twXYtQNTanbJwXcXad7csSylIl1rqDJF51BCWHz2Z6WmACfadvjnQXHPgttk6QDoVW+PYDgAWjZhvWdA1kB4dbmGKl9eZ5xNBId9t3wzvkmtZNtBDv6GHuAdlhv5Qw8FKnnNI6BvgWL1i+fS8wxCs0salPO3PdNLiCWNwFKMrazZ5CFnzMiDewpX3L8zob/Ww+rqGlO/Hx6kbH+PMz3wwZu7U9o4s197ufwS9vCuYWO8tu195NNUrd17z1/sKwJbb91ACpWZpgzLmXSM4OiecCIZkqsku9Cvey+3oTpD6gFbUSvkKme6T2dY3Te+PClafHG7nWXlmSmzQMmlC/Ij3S5VsmlrIm51AGnlCYyiqsBQeyOwKtERnhePXbdlFiwtEHFbBdBol3O3ZDSH4MVJ5IhYSeyZPbXSCgXO73JOxl5mBX36nCC2pHR3x2yP9m3Je9dteUYfRNbalitRl98lojtQyTAACdb2DGlu24unrqSBfXdXJk5TV+gK29sEMuhLbYigkgQtkmIDV4zaisaZqx3voHDuMYnz6hxoPqPVR34XokThgkiAAfe70zz8rQ+IfXVY5ECdYeu0n3EQzEcbCY2id66vRc/EH7oGGR+jVkCjvpwhJstb26PEUiCuQI06U0Jn+IZCnwQNE1xHkhOQW0yuw+c9ukyEGOvjidVVG8CMrUCYyY+OqudddQPGu9LOpnjnlTlS1hBVT4cTJxhS2qqiZgnzeli2yDmvCWtkwxtEwB1UbFb4CbD+q0ha0ouudIEHOvSrBTHKvGuM7O5MiMJfsB2XgBqwVOTblrP4Q30wrWkEf42UtYU+EyfImPvDO06zJA3sZJCCqtfqOiT+UEFSqSxCmOV4plfTIkbnWSecbwF3/AegHrMe5cQ0tKKd+YNYGL9lFyIKcxBb0tyLi4RYJl8NPPdg+7CbjGzJ8YBQMPfRR9u8hGgCARQSGU1Acu2K3IQGcjfFTp5SkM4ngYZg4eai8NLx3/+WHz9XdwguVER4TYM/pHzhaghhS/H5AoKxK6NwlwZR3jntvrw0W1O6oVNQFWtj0ywRBvLCV1v+LxS/tWap6utTmy6D8PwTLufPJUY0XbBcqaD6O4qFxgNrHdmreV5cs7xNfqmdFwPNuWLb/bOuJSDywrd90GautV778EjthsFXYn0Pi6OL3mARdQgBI3PDPtv7vZLdZzVYFMY8HcS+01/BZM6pAt9MgL+Yql9icEmKQAj0I5CxMFmPJ/3mCIH+TeIY6ru2nAesecmVx/albe3vVMy3oJ3XPS5bKFRMacmW6YoaV4TQI58AVqEgpmjtwOyo3Sz4ZFyN/0IKZFnbLaZS1gnnNPS74j63LhWxL7XMgpap57JKjyewSjSlvWAfZOzVJxnP6yktHgnVGyVLGP7u/6dflQPGxDuJ0MgArg8vws/RbXKbIWNfcq8U++td3OlwTgfItCzoafmaCS+Qo0jnoRdtafj4umxwnS3Wmqf2B9Zf5vw00dcdrbzXtTvxCgYMqaXRND5uNiPmmJ3XLdDcMNfpUd7zJTrmsT10dXDhVN+HOP58dJamRI1vZjZUtk+vbPUYhXNatG6AqiifxGj+u8Vbw3GamY/S9spAl8KX2FiGlohO+nSbShnBWIL7IpbuJ4J9K4BMJ3hCtLMz88eE7XDNRel1Lf4keCwnCWfjPp+YLKX893aFWyYfhvfR3QqBfYsajCAKgLSGFQaOcGkWuoikth0zFNrrC68KQyayqyLlLXlFH7xiwT0+DbCEH6TFOLJnbZHV7sskJFwt7T5YlvNGhwPgoVX+3C5ao3pajSjWRPaDnYSbzXt6KQQqS2VNw0RLshmbAdgyP3gpooZYhNwBVtqnWbsBZiVJ9WqdRUuvsnu+1FLR7BQWqBbtEbO2OXbFeDiYLTMLq47AH6cE8oam2J5766sEeEda8AQdJsoNYH7ATjBO78sMxGS7R/vdEwQw2QNYXT3Of5ZvyZd9O8+f0HW5lACVaDX7W7lgyHsaX7yiOpgUjWERU+gNC5i+smxP8r7XxbQf4x6Dxag+kf0l9Wy7X/6LuIiP5a8qdNJkvkuxwGEZZccKwCDlTSDzoGEkelxP3UvjkRPbRJP6GmJZ10B7w8UehYv2o5C6O6N4Xm8OefHzjkR1uJMQBiIBF3XmANxY3FFPX/wVfi3xS03dhZYgdnqFE7zC+WR1p77pjzgm7WVBQj7MX3vywENmpKJjpLFMQYjQgCSkxlDyoCjgjWpkmxE2147OenSGICUX7vtpzYz/97ZxfzjOkxZWYTsjswq8AA6T2jq65hv4c31cq8lK12g9GRTc0+jp6j9NjU/wl38omJUUy8MCJopoSq2blHRKLb8GYYcPx8PDlMYZvi0zTH+70lYkXTvwiHHH6GsUFtc0a+tJCLDOSiYREStjJuafpGfE7Zsl9mG+ybzEpyHb1PHQnCbSRA1TsMH3uYvncz8mRQRg5b5MCtpNggcAzfvAIPOc50xZecZtnwG3jr+zmSFCrKI3cZJsVxVpmfPucUFRoqBCtIEJZ4u3hmwhfSuGt0ldNMBDa+KYX5kStuZuJ+ma8c3/xi1r/gK9EEbZYmmVPT/OEkOjSjiqfBLKYjQOvPEPlZ4dlh4Gsiaan1pVU9uvmtwl1Wd6t1mhQUpwcepQi/feTwBSYpwOE3Hc2mn64zIHeIB2zXfxpmBZw8LEnyy/38/977+//vFTV9uJyMrIjqy0981HWipb2IZu8MxwEmGeU3TfJRSgUx2WjlNwJe'

print("Decoding fourth layer...")
try:
    fourth_result = _(fourth_payload)
    print("Fourth layer decoded successfully!")
    print(fourth_result.decode('utf-8'))

    print("\n" + "="*50 + "\n")

    # Fifth layer
    fifth_payload = b'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'

    print("Decoding fifth layer...")
    try:
        fifth_result = _(fifth_payload)
        print("Fifth layer decoded successfully!")
        print(fifth_result.decode('utf-8'))

        print("\n" + "="*50 + "\n")

        # Sixth layer
        sixth_payload = b'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'

        print("Decoding sixth layer...")
        try:
            sixth_result = _(sixth_payload)
            print("Sixth layer decoded successfully!")
            print(sixth_result.decode('utf-8'))
        except Exception as e:
            print(f"Error decoding sixth layer: {e}")

    except Exception as e:
        print(f"Error decoding fifth layer: {e}")

except Exception as e:
    print(f"Error decoding fourth layer: {e}")
